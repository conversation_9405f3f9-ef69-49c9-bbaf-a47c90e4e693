# Redis队列长度控制功能

## 概述

为了防止ORC处理服务产生数据的速度超过MongoDB写入服务消费数据的速度，导致Redis队列无限增长，我们实现了队列长度控制机制。当Redis队列长度超过设定阈值时，ORC处理服务会暂停处理，直到队列长度降到安全水平。

## 功能特性

### 1. 自动暂停/恢复机制
- **暂停阈值**：当队列长度超过 `pause_threshold` 时，暂停ORC文件处理
- **恢复阈值**：当队列长度降到 `resume_threshold` 以下时，恢复ORC文件处理
- **最大等待时间**：超过 `max_wait_time` 后强制恢复处理，避免长时间阻塞

### 2. 实时监控
- 提供 `/queue/status` API端点查看队列状态
- 记录暂停/恢复事件和持续时间
- 支持队列长度实时监控

### 3. 配置灵活性
- 所有参数都可通过配置文件调整
- 支持开发环境和生产环境不同的配置
- 可以根据系统负载动态调整阈值

## 配置参数

### 开发环境配置 (development.yaml)
```yaml
redis:
  queue_control:
    # 队列长度检查间隔（秒）
    check_interval: 5
    # 暂停阈值：队列长度超过此值时暂停ORC处理
    pause_threshold: 8000
    # 恢复阈值：队列长度低于此值时恢复ORC处理
    resume_threshold: 5000
    # 最大等待时间（秒），超过此时间强制继续处理
    max_wait_time: 300
```

### 生产环境配置 (production.yaml)
```yaml
redis:
  queue_control:
    # 队列长度检查间隔（秒）
    check_interval: 10
    # 暂停阈值：队列长度超过此值时暂停ORC处理
    pause_threshold: 40000
    # 恢复阈值：队列长度低于此值时恢复ORC处理
    resume_threshold: 25000
    # 最大等待时间（秒），超过此时间强制继续处理
    max_wait_time: 600
```

## API接口

### 获取队列状态
```bash
GET http://localhost:8001/queue/status
```

响应示例：
```json
{
  "queue_name": "mongodb_write_queue",
  "queue_length": 12500,
  "is_paused": true,
  "pause_duration": 45.2,
  "thresholds": {
    "pause_threshold": 8000,
    "resume_threshold": 5000
  },
  "status": "paused"
}
```

## 工作流程

1. **正常处理状态**
   - ORC处理服务正常读取文件并发送到Redis队列
   - MongoDB写入服务从队列消费数据并写入数据库

2. **队列长度检查**
   - 每隔 `check_interval` 秒检查一次队列长度
   - 在发送数据到队列前进行检查

3. **暂停处理**
   - 当队列长度 >= `pause_threshold` 时触发暂停
   - 记录暂停开始时间和原因
   - 停止处理新的ORC文件

4. **等待恢复**
   - 定期检查队列长度是否降到 `resume_threshold` 以下
   - 或者等待时间是否超过 `max_wait_time`

5. **恢复处理**
   - 满足恢复条件时重新开始处理
   - 记录暂停持续时间

## 测试工具

### 使用测试脚本
```bash
# 测试队列控制阈值
python3 services/orc_mongodb_service/test_queue_control.py --action test

# 监控队列长度变化
python3 services/orc_mongodb_service/test_queue_control.py --action monitor --duration 300

# 向队列添加测试消息
python3 services/orc_mongodb_service/test_queue_control.py --action add --count 10000

# 清空队列
python3 services/orc_mongodb_service/test_queue_control.py --action clear
```

## 日志示例

### 暂停处理日志
```
2025-07-22 10:30:15 [WARNING] ORCProcessorService: Redis队列长度达到暂停阈值 8000，当前长度: 8150，暂停ORC处理
```

### 恢复处理日志
```
2025-07-22 10:32:45 [INFO] ORCProcessorService: Redis队列长度降至恢复阈值 5000，当前长度: 4850，恢复ORC处理，暂停时长: 150.3秒
```

### 强制恢复日志
```
2025-07-22 10:35:15 [WARNING] ORCProcessorService: 队列暂停时间超过最大等待时间 300秒，强制恢复处理，当前队列长度: 7200
```

## 性能影响

### 优势
- **防止内存溢出**：避免Redis队列无限增长导致内存不足
- **系统稳定性**：保持生产者-消费者平衡
- **自动恢复**：无需人工干预，系统自动调节

### 注意事项
- **处理延迟**：暂停期间会增加整体处理时间
- **配置调优**：需要根据实际负载调整阈值参数
- **监控重要性**：建议配置监控告警，及时发现队列积压问题

## 故障排除

### 常见问题

1. **队列长度持续增长**
   - 检查MongoDB写入服务是否正常运行
   - 确认MongoDB连接和写入性能
   - 考虑增加MongoDB写入服务实例

2. **频繁暂停/恢复**
   - 调整 `pause_threshold` 和 `resume_threshold` 的差值
   - 增大 `check_interval` 减少检查频率
   - 优化MongoDB写入性能

3. **长时间暂停**
   - 检查 `max_wait_time` 设置是否合理
   - 确认MongoDB写入服务状态
   - 考虑临时增大队列容量

### 监控建议

1. **队列长度监控**：设置队列长度告警阈值
2. **暂停频率监控**：监控暂停/恢复事件频率
3. **处理延迟监控**：监控端到端处理时间
4. **系统资源监控**：监控Redis内存使用情况
