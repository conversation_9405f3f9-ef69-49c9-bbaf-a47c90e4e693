# ORC数据处理微服务 - 开发环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "development"

# ==================== 服务配置 ====================
service:
  name: "orc_processor_service"
  host: "0.0.0.0"
  port: 8001
  workers: 1

# ==================== 日期和省份配置 ====================
# 处理开始日期 (YYYYMMDD格式)
start_date: "20250629"

# 处理结束日期 (YYYYMMDD格式)
end_date: "20250630"

# 省份ID列表 - 开发环境处理单个省份
province_ids: [200]

# ==================== ORC文件配置 ====================
# ORC文件基础路径
orc_base_path: "/workdir/hive_data/tw_user_pic_daily_aggregation"

# ORC文件匹配模式
orc_file_pattern: "*"

# 支持的ORC文件列名映射
column_mapping:
  uid_columns: ["id", "uid", "user_id", "userid", "UID", "USER_ID"]
  pid_columns: ["pic_id_list", "pid_list", "pid", "product_id", "item_id", "PID", "PRODUCT_ID"]

# ==================== 数据处理配置 ====================
# 每个用户最多保留的PID数量
max_pids_per_user: 300

# 是否启用PID去重
enable_pid_deduplication: true

# 是否按时间戳排序PID
sort_pids_by_timestamp: true

# PID存储优化：是否按时间戳分组
group_pids_by_timestamp: true

# ==================== 分批处理配置 ====================
batch_processing:
  # 用户处理批次大小
  batch_size: 1000
  # PID查询批次大小（发送给Milvus）
  pid_query_batch_size: 15000
  # 是否启用批量PID查询优化
  enable_batch_optimization: true

# ==================== Milvus配置 ====================
milvus:
  # 连接配置
  connection:
    uri: "http://localhost:19530"
    token: ""
    database: "default"
    
    # 连接池配置
    pool:
      max_connections: 20
      min_connections: 5
      timeout: 30
      max_retries: 3
      retry_delay: 1.0
  
  # 集合配置
  collections:
    content_collection: "content_tower_collection_20250616"
    user_collection: "user_tower_collection"
  
  # 向量维度配置
  vector_dimensions:
    content_vector_dim: 512
    user_vector_dim: 256
  
  # 批处理配置
  batch_processing:
    timeout: 300
    concurrent_batches: 5

# 是否启用Milvus PID过滤
enable_milvus_filtering: true

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  
  # 队列配置
  queue_name: "mongodb_write_queue"
  max_queue_size: 10000
  
  # 连接池配置
  connection_pool:
    max_connections: 20
    retry_on_timeout: true

# ==================== 监控和日志配置 ====================
# 进度报告间隔（秒）
progress_report_interval: 30

# 统计输出间隔（秒）
stats_output_interval: 60

# 是否启用详细统计
enable_detailed_stats: true

# 是否启用详细日志
enable_verbose_logging: true

# ==================== 日志配置 ====================
logging:
  level: DEBUG
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/orc_processor_service/orc_processor_service.log
  file_max_size: "50MB"
  file_backup_count: 5
  console_enabled: true
  console_colored: true
  structured: false

# ==================== 性能优化配置 ====================
performance:
  # 内存优化
  memory:
    gc_threshold: 0.8
    cleanup_interval: 300
  
  # I/O优化
  io:
    read_buffer_size: 8192
    write_buffer_size: 8192
    use_async_io: true
  
  # 并发配置
  concurrency:
    max_concurrent_files: 5
    max_concurrent_batches: 10
